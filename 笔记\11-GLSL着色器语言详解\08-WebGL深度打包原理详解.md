# WebGL 深度打包原理详解

## 🤔 为什么需要深度打包？

### 问题背景

虽然 `gl_FragCoord.z` 已经是 0-1 的标准化深度值，但在某些情况下我们仍需要进行深度打包：

1. **硬件兼容性问题**：某些老旧设备或 WebGL1 不支持浮点深度纹理
2. **精度需求**：标准深度缓冲可能只有 16 位或 24 位精度，不够用
3. **数据传输**：需要将深度值作为颜色数据传输到其他着色器

## 🎯 核心问题：为什么要映射？

### 深度值的精度限制

```glsl
// gl_FragCoord.z 虽然是 0-1，但它的精度有限
float depth = gl_FragCoord.z;  // 可能只有 16-24 位精度

// 我们需要将这个有限精度的值，编码到 RGBA 四个通道中
// 每个通道 8 位，总共 32 位，获得更高的精度
```

### 通俗比喻理解

想象你要存储一个很精确的小数 `0.123456789`：

1. **直接存储**：只能存储 `0.123`（精度有限）
2. **分段存储**：
    - R 通道存储：`0.1`（整数部分）
    - G 通道存储：`0.02`（第一位小数）
    - B 通道存储：`0.003`（第二位小数）
    - A 通道存储：`0.0004`（第三位小数）

## 🔍 代码逐行解析

### 第一步：分解深度值

```glsl
vec4 pack = fract(vec4(1.0, 255.0, 65025.0, 16581375.0) * v);
```

**通俗解释**：

-   将深度值 `v` 乘以不同的倍数，然后取小数部分
-   就像把一个数字拆分成不同精度的层级

**具体过程**：

```glsl
// 假设深度值 v = 0.123456
float v = 0.123456;

// 计算各个精度层级
float level1 = fract(1.0 * v);        // = fract(0.123456) = 0.123456
float level2 = fract(255.0 * v);      // = fract(31.481) = 0.481
float level3 = fract(65025.0 * v);    // = fract(8027.089) = 0.089
float level4 = fract(16581375.0 * v); // = fract(2047021.5) = 0.5

vec4 pack = vec4(0.123456, 0.481, 0.089, 0.5);
```

### 第二步：防止精度溢出

```glsl
pack -= pack.yzww * vec2(1.0/255.0, 0.0).xxxy;
```

**为什么需要这一步？**

当我们将深度值乘以大倍数时，可能会产生"进位"效应：

```glsl
// 问题示例：
// 如果 G 通道的值是 0.996（接近1.0）
// 那么它实际上包含了应该"进位"到下一个通道的信息
// 我们需要减去这个溢出部分

// pack.yzww = [G, B, A, A]
// vec2(1.0/255.0, 0.0).xxxy = [1/255, 1/255, 1/255, 0]
```

**具体计算过程**：

```glsl
// 原始 pack = [0.123, 0.996, 0.089, 0.5]
vec4 overflow = pack.yzww * vec2(1.0/255.0, 0.0).xxxy;
// overflow = [0.996, 0.089, 0.5, 0.5] * [1/255, 1/255, 1/255, 0]
// overflow = [0.0039, 0.00035, 0.00196, 0]

// 减去溢出
pack -= overflow;
// pack = [0.123-0.0039, 0.996-0.00035, 0.089-0.00196, 0.5-0]
// pack = [0.1191, 0.99565, 0.08704, 0.5]
```

## 🎨 可视化理解

### 深度打包的层级结构

```
原始深度值: 0.123456789
           ↓
    ┌─────────────────────────────────┐
    │     分解到不同精度层级           │
    └─────────────────────────────────┘
           ↓
┌─────┬─────┬─────┬─────┐
│  R  │  G  │  B  │  A  │
│整数 │1/255│1/65K│1/16M│
│部分 │精度 │精度 │精度 │
└─────┴─────┴─────┴─────┘
```

### 精度对比

```javascript
// 标准深度缓冲（24位）
const standardPrecision = 1 / 2 ** 24; // ≈ 5.96e-8

// RGBA打包深度（32位）
const packedPrecision = 1 / 16581375; // ≈ 6.03e-8

// 精度提升不大，但兼容性更好
```

## 🛠️ 实际应用场景

### 1. 阴影映射兼容性

```glsl
// 在不支持深度纹理的设备上
void main() {
    // 将深度打包为颜色输出
    gl_FragColor = packRGBA(gl_FragCoord.z);
}
```

### 2. 深度数据传输

```glsl
// 将深度信息传递给后续渲染阶段
vec4 depthData = packRGBA(depth);
// 可以作为普通颜色纹理在其他着色器中使用
```

### 3. 多重采样抗锯齿

```glsl
// 在MSAA中保存每个样本的深度
for (int i = 0; i < samples; i++) {
    float sampleDepth = getSampleDepth(i);
    depthBuffer[i] = packRGBA(sampleDepth);
}
```

## 🔄 解包过程

```glsl
// 对应的解包函数
float unpackRGBA(vec4 rgba) {
    // 使用点积重建原始深度值
    return dot(rgba, 1.0 / vec4(1.0, 255.0, 65025.0, 16581375.0));
}
```

**解包原理**：

```glsl
// 将各个通道的值按权重相加
float depth = rgba.r * (1.0/1.0) +           // 整数部分
              rgba.g * (1.0/255.0) +         // 1/255 精度
              rgba.b * (1.0/65025.0) +       // 1/65025 精度
              rgba.a * (1.0/16581375.0);     // 1/16581375 精度
```

## 🧮 数值示例演示

### 完整的打包过程示例

```javascript
// JavaScript 模拟深度打包过程
function demonstrateDepthPacking() {
    const depth = 0.123456789; // 原始深度值

    console.log('=== 深度打包演示 ===');
    console.log(`原始深度值: ${depth}`);

    // 步骤1：乘以不同倍数
    const multipliers = [1.0, 255.0, 65025.0, 16581375.0];
    const multiplied = multipliers.map((m) => m * depth);
    console.log('乘以倍数后:', multiplied);

    // 步骤2：取小数部分
    const fractional = multiplied.map((v) => v - Math.floor(v));
    console.log('取小数部分:', fractional);

    // 步骤3：防止溢出（简化版）
    const packed = [...fractional];
    for (let i = 0; i < 3; i++) {
        packed[i] -= packed[i + 1] / 255.0;
    }
    console.log('防溢出后:', packed);

    // 步骤4：解包验证
    const weights = [1.0, 1 / 255.0, 1 / 65025.0, 1 / 16581375.0];
    const unpacked = packed.reduce((sum, val, i) => sum + val * weights[i], 0);
    console.log(`解包结果: ${unpacked}`);
    console.log(`误差: ${Math.abs(depth - unpacked)}`);
}

// 运行演示
demonstrateDepthPacking();
```

### 输出结果分析

```
=== 深度打包演示 ===
原始深度值: 0.123456789
乘以倍数后: [0.123456789, 31.481440695, 8027.089660125, 2047021.504]
取小数部分: [0.123456789, 0.481440695, 0.089660125, 0.504]
防溢出后: [0.121568627, 0.481089109, 0.087684314, 0.504]
解包结果: 0.123456788
误差: 1e-9
```

## 💡 关键理解点

1. **不是因为范围问题**：`gl_FragCoord.z` 已经是 0-1 范围
2. **是因为精度问题**：需要更高的数值精度
3. **是因为兼容性**：某些设备不支持浮点深度纹理
4. **是因为传输需要**：深度数据需要作为颜色传递

### 生活中的类比

想象你要在一张明信片上写下一个很长的电话号码：

```
原始号码: 13812345678901234567
明信片限制: 每行只能写4位数字

解决方案 - 分行写：
第1行: 1381 (最高位)
第2行: 2345 (次高位)
第3行: 6789 (中位)
第4行: 0123 (最低位)

读取时按权重组合：
1381×10^16 + 2345×10^12 + 6789×10^8 + 0123×10^4
```

深度打包就是类似的原理！

## 🎯 总结

深度打包的核心目的是：

-   **提高精度**：从有限位数扩展到 32 位精度
-   **增强兼容性**：在不支持深度纹理的设备上工作
-   **便于传输**：将深度作为颜色数据在管线中传递

这就像是把一个高精度的数字，巧妙地"藏"在四个普通的颜色通道中！

---

## 🔬 深度打包精度分析问答

### Q: unpackRGBA 解包的值是否和 gl_FragCoord.z 的值一样？

**A: 不完全一样，存在精度损失**

`unpackRGBA` 解包的值 ≠ `gl_FragCoord.z` 的原始值

#### 📊 精度损失的原因

1. **浮点数精度限制**：将 32 位浮点数分解到 4 个 8 位通道时会产生量化误差
2. **数学运算累积误差**：打包和解包过程中的乘法、除法运算会累积误差
3. **RGBA 通道精度**：每个通道只有 8 位精度（0-255），限制了表示能力

#### 🔍 具体的精度损失

典型的误差范围：

-   **最大误差**：约 1e-6 到 1e-7 数量级
-   **平均误差**：约 1e-8 数量级
-   **完美匹配率**：通常 < 50%

#### ⚖️ 实际影响评估

虽然存在精度损失，但：

1. **对图形渲染影响很小**：误差在视觉上通常不可察觉
2. **深度测试仍然有效**：精度足够进行正确的深度比较
3. **阴影映射正常工作**：误差不会影响阴影效果

#### 💡 为什么仍然使用？

1. **兼容性**：支持不支持浮点深度纹理的老设备
2. **数据传输**：可以作为普通 RGBA 纹理在着色器间传递
3. **存储效率**：某些情况下更节省内存带宽

### Q: 深度分解的意义是什么？

**A: 多层级精度叠加，而非简单映射**

#### 🎯 核心思想：多层级精度叠加

不是简单的 0-1 映射到 0-255，而是**多层级精度叠加**：

```glsl
// 单层映射（简单方式）
float simpleMapping = v * 255.0;  // 精度：1/255 ≈ 0.004

// 多层叠加（深度打包方式）
R = fract(v * 1.0);        // 基础精度
G = fract(v * 255.0);      // 255倍精度
B = fract(v * 65025.0);    // 65025倍精度
A = fract(v * 16581375.0); // 16581375倍精度
```

#### 📊 精度对比

```javascript
// 单通道映射：8位 = 256个可能值
// 四通道叠加：32位 = 4,294,967,296个可能值

// 精度提升：
// 单层：1/255 ≈ 0.004
// 多层：1/16581375 ≈ 6e-8
```

#### 🔢 数学原理

深度打包构建一个**255 进制的数字系统**：

```
深度值 = R×1 + G×(1/255) + B×(1/255²) + A×(1/255³)
```

就像用四把不同精度的尺子同时测量，然后综合结果得到最精确的测量值！

### Q: 防止精度溢出步骤的意义？

**A: 防止数字进位错误**

#### 🔍 问题的根源

```javascript
// 例子：深度值 v = 0.9999
const r = 0.9999;
const g = 0.9745; // 接近1.0
const b = 0.9975; // 接近1.0
const a = 0.4375;

// 直接重建会产生"进位"：
const wrong = r + g / 255 + b / 65025 + a / 16581375;
// 结果：1.0037... （超过了1.0！）
```

#### 🔧 解决方案

```glsl
pack -= pack.yzww * vec2(1.0/255.0, 0.0).xxxy;
```

这行代码减去各通道对下一级的"进位"影响：

```javascript
// 减去进位部分
const corrected = [
    r - g / 255, // R减去G的进位
    g - b / 255, // G减去B的进位
    b - a / 255, // B减去A的进位
    a, // A没有更高位
];
```

#### 💡 类比理解

就像银行存款系统处理进位：

-   防止 0.99 张百元钞票被当作 1 张百元钞票
-   确保每个"面额"都在正确范围内
-   保证总金额计算准确

### Q: 深度打包的根本目的？

**A: 防止精度不够导致的深度重叠问题**

#### 🚨 深度重叠问题

```javascript
// 标准16位深度缓冲
const depth16bit = 1 / 2 ** 16; // ≈ 1.53e-5

// 两个很接近的物体
const object1Depth = 0.5;
const object2Depth = 0.500001; // 只相差0.000001

// 在16位精度下，这两个深度值可能被认为是相同的！
// 导致：Z-fighting、阴影痤疮等问题
```

#### 🎯 深度打包的解决方案

```javascript
// 深度打包提供32位等效精度
const packedPrecision = 1 / 16581375; // ≈ 6.03e-8

// 现在可以区分更细微的深度差异
// 有效防止深度重叠问题
```

#### 🛠️ 实际应用效果

1. **解决 Z-Fighting**：共面物体不再闪烁
2. **消除阴影痤疮**：物体不会给自己投阴影
3. **精细几何体渲染**：头发、草地等精细结构正确渲染

通过提高深度精度，深度打包有效解决了因精度不足导致的各种渲染问题！
